import axios from "axios";
import qs from "qs";

interface IStrapiResponse {
  data: {
    data: any[];
    meta: {
      pagination: {
        total: number;
      };
    };
  };
}

function formatComponentString(dynamicZone: any, componentPrefix: string): any {
  const newDynamicZone = dynamicZone.map((c: any) => {
    c.__component = c.__component.replace(`${componentPrefix}.`, "");
    return c;
  });

  return newDynamicZone;
}

async function strapiRequest(
  endpoint: string,
  query: object,
): Promise<IStrapiResponse | { error: string }> {
  const qsSlug = qs.stringify(query, {
    encodeValuesOnly: true,
  });
  const r: IStrapiResponse = await axios.get(`${process.env.CMS_URL}/api/${endpoint}?${qsSlug}`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });

  if (r.data.meta.pagination.total === 0) {
    return {
      error: "Not Found",
    };
  }

  return r;
}

interface IFlag {
  name: string;
  flag?: string;
  points: number;
  phase: string;
}

async function getFlags(): Promise<IFlag[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-flags", {
    populate: {
      tda_core_phase: {
        fields: ["state"],
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  const flags = r.data.data.map((d) => ({
    name: d.name,
    points: d.points,
    phase: d.tda_core_phase.state,
  }));

  return flags;
}

interface ITeamItems {
  tda_core_team: {
    name: string;
  };
  tda_ctf_item: {
    boardPosition: number;
    components: any[];
  };
}

async function getTeamItems(teamId: string): Promise<ITeamItems[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-team-items", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_item: {
        fields: ["*"],
        populate: ["components"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  r.data.data.forEach((d) => {
    d.tda_core_team.id = undefined;
    d.tda_ctf_item.id = undefined;
    d.tda_ctf_item.documentId = undefined;
    d.tda_ctf_item.createdAt = undefined;
    d.tda_ctf_item.updatedAt = undefined;
    d.tda_ctf_item.publishedAt = undefined;
    d.tda_ctf_item.components = formatComponentString(
      d.tda_ctf_item.components,
      "td-a-ctf-platform-items",
    );
  });

  return r.data.data;
}

export { getFlags, getTeamItems };
