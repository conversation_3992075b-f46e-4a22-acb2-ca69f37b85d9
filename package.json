{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@pinia/nuxt": "0.11.2", "@tailwindcss/vite": "^4.1.11", "@types/qs": "^6.14.0", "axios": "^1.10.0", "biome": "^0.3.3", "nuxt": "^4.0.1", "pinia": "^3.0.3", "vite-tsconfig-paths": "^5.1.4", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "tailwindcss": "^4.1.11", "postcss": "^8.4.39", "prettier": "^3.2.5", "vue-tsc": "^2.0.21", "@vue/tsconfig": "^0.5.1"}}