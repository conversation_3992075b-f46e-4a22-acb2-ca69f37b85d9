import { getConnections } from "~~/server/utils/strapi";

type action = "disconnect" | "connect" | "update";

interface item {
  documentId: string;
}

interface connectionAction {
  source: item;
  target: item;
  action: action;
}

interface IBoardRequest {
  teamId: string;
  items: connectionAction[];
}

async function connectItems(teamId: string, items: connectionAction) {
  const sourceConnection = await getConnections(teamId, items.source.documentId);
  const targetConnection = await getConnections(teamId, items.target.documentId);
  return {
    sourceConnection,
    targetConnection,
  };
}

export default defineEventHandler(
  async (event): Promise<any | { status: string } | { error: string }> => {
    const body: IBoardRequest = await readBody<IBoardRequest>(event);
    if (!body.teamId) {
      // TODO: After keycloak implementation get teamId from keycloak and not via post req
      return {
        error: "No team ID provided",
      };
    }

    if (!body.items) {
      return {
        error: "No items provided",
      };
    }

    for (const item of body.items) {
      switch (item.action) {
        case "connect":
          return await connectItems(body.teamId, item);
        //   case "disconnect":
        //     await disconnectItems(item);
        //     break;
        //   case "update":
        //     await updateItems(item);
        //     break;
      }
    }

    return body;
  },
);
